import { TRPCError } from "@trpc/server";
import type { Gender } from "~/lib/enums/enums";
import { type SnowDiscipline, type SnowEvent } from "~/lib/enums/snow";
import type {
  SnowFeature,
  SnowRun,
  SnowsportRace,
} from "~/lib/interfaces/externalCall";
import { callPta } from "./pta";

export const getSnowsportRaces = async ({
  token,
  competitionId,
}: {
  token: string;
  competitionId: string;
}) => {
  try {
    const url = `snowsports/events?competition_id=${competitionId}&include_runs=true`;
    const res = await callPta<SnowsportRace[]>(url, {
      method: "GET",
      token,
    });
    return res;
  } catch (error) {
    console.error(error);
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to fetch snow sport races",
    });
  }
};

export const getSnowRace = async ({
  token,
  raceId,
}: {
  token: string;
  raceId: string;
}) => {
  const url = `snowsports/events/${raceId}`;
  const res = await callPta<SnowsportRace>(url, {
    method: "GET",
    token,
  });
  res.results.sort((a, b) =>
    a.athlete.first_name.localeCompare(b.athlete.first_name),
  );
  return res;
};

export const upsertSnowRace = async ({
  id,
  competitionId,
  round,
  date,
  gender,
  event,
  discipline,
}: {
  id?: string;
  competitionId: string;
  round: string;
  date: string;
  gender: Gender;
  event: SnowEvent;
  discipline: SnowDiscipline;
}) => {
  let url = "snowsports/events/";
  if (id) {
    url += id;
  }

  const res = await callPta<SnowsportRace>(url, {
    method: id ? "PUT" : "POST",
    body: JSON.stringify({
      competition_id: competitionId,
      round,
      date,
      gender,
      event,
      discipline,
    }),
  });
  return res;
};

export const upsertRun = async ({
  id,
  resultId,
  run,
  score,
  sectionScore,
  overallScore,
}: {
  id?: string;
  resultId: string;
  run: number;
  score?: number | null;
  sectionScore?: number | null;
  overallScore?: number | null;
}) => {
  let url = "snowsports/runs";
  if (id) {
    url += id;
  }

  const res = await callPta<SnowRun>(url, {
    method: id ? "PUT" : "POST",
    body: JSON.stringify({
      result_id: resultId,
      run,
      score,
      section_score: sectionScore,
      overall_score: overallScore,
    }),
  });

  return res;
};

export const deleteRun = async ({ id }: { id: string }) => {
  const url = `snowsports/runs/${id}`;

  const res = await callPta<SnowRun>(url, {
    method: "DELETE",
  });

  return res;
};

export const upsertFeature = async ({
  id,
  runId,
  trickNum,
  trick,
  featureNum,
}: {
  id?: string;
  runId: string;
  trickNum: number;
  trick: { switch: boolean; cab: boolean };
  featureNum: number;
}) => {
  let url = "snowsports/features";
  if (id) {
    url += id;
  }

  const res = await callPta<SnowFeature>(url, {
    method: id ? "PUT" : "POST",
    body: JSON.stringify({
      run_id: runId,
      trick_num: trickNum,
      trick,
      feature_num: featureNum,
    }),
  });
};
